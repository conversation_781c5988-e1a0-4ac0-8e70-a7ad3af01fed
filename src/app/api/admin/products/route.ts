import { NextRequest } from "next/server";
import { withAdminAuth } from "@/lib/admin/api/middleware";
import { ProductController } from "@/lib/admin/controllers/ProductController";

const productController = new ProductController();

// GET /api/admin/products - <PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm cho admin
export async function GET(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await productController.handleList(request);
  });
}

// POST /api/admin/products - Tạo sản phẩm mới
export async function POST(request: NextRequest) {
  return withAdminAuth(request, async () => {
    return await productController.handleCreate(request);
  });
}
