const fetch = require('node-fetch');

async function testSignIn() {
  try {
    console.log('🔐 Testing sign-in process...');
    
    // Test credentials
    const credentials = {
      email: '<EMAIL>',
      password: 'user123'
    };
    
    // Sign in
    const signInResponse = await fetch('http://localhost:3001/api/auth/callback/credentials', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        email: credentials.email,
        password: credentials.password,
        csrfToken: 'test', // This might need to be obtained first
        callbackUrl: 'http://localhost:3001',
        json: 'true'
      })
    });
    
    console.log('Sign-in response status:', signInResponse.status);
    const signInData = await signInResponse.text();
    console.log('Sign-in response:', signInData);
    
  } catch (error) {
    console.error('❌ Error testing sign-in:', error);
  }
}

testSignIn();
